﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Entities.Concrete;
using Core.Utilities.Business;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserManager : IUserService
    {
        IUserDal _userDal;

        public UserManager(IUserDal userDal)
        {
            _userDal = userDal;
        }
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        //buraya üye olurken girildiğinden secured operation aspect koyma
        public IResult Add(User user)
        {
            _userDal.Add(user);
            return new SuccessResult(Messages.UserAdded);
        }
        [PerformanceAspect(3)]
        //buraya üye olurken girildiğinden secured operation aspect koyma
        public List<OperationClaim> GetClaims(User user)
        {
            return _userDal.GetClaims(user);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int id)
        {
            _userDal.Delete(id);
            return new SuccessResult(Messages.UserDeleted);
        }
        [SecuredOperation("owner")]
        public IDataResult<List<User>> GetAll()
        {
            return new SuccessDataResult<List<User>>(_userDal.GetAll());
        }
        //buraya login olurken veri çekildiği için secured operation aspect koyma
        public User GetByMail(string email)
        {
            return _userDal.Get(u => u.Email == email);
        }
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        ////[SecuredOperation("owner")]
        //[ValidationAspect(typeof(UserValidator))]
        public IResult Update(User user)
        {
            // Önce eski User bilgilerini al
            var oldUser = _userDal.Get(u => u.UserID == user.UserID);

            // User tablosunu güncelle
            _userDal.Update(user);

            // Eğer email, firstName veya lastName değişmişse CompanyUser tablosunu da güncelle
            if (oldUser != null &&
                (oldUser.Email != user.Email ||
                 oldUser.FirstName != user.FirstName ||
                 oldUser.LastName != user.LastName))
            {
                SyncCompanyUserData(user, oldUser);
            }

            return new SuccessResult(Messages.UserUpdated);
        }

        //[SecuredOperation("owner,admin")]
        public IDataResult<User> GetById(int userId)
        {
            var user = _userDal.Get(u => u.UserID == userId && u.IsActive);
            if (user == null)
            {
                return new ErrorDataResult<User>(Messages.UserNotFound);
            }
            return new SuccessDataResult<User>(user);
        }

        /// <summary>
        /// User tablosu güncellendiğinde CompanyUser tablosundaki ilgili kayıtları senkronize eder
        /// </summary>
        private void SyncCompanyUserData(User updatedUser, User oldUser)
        {
            try
            {
                using (GymContext context = new GymContext())
                {
                    // Bu User'a ait CompanyUser kayıtlarını bul
                    // UserCompanies tablosu üzerinden CompanyUser'ları bul
                    var companyUsers = (from uc in context.UserCompanies
                                       join cu in context.CompanyUsers on uc.UserID equals cu.CompanyUserID
                                       where uc.IsActive == true && cu.IsActive == true
                                       select cu).ToList();

                    // Email değişmişse eski email ile CompanyUser bul
                    if (oldUser.Email != updatedUser.Email)
                    {
                        var companyUsersByEmail = context.CompanyUsers
                            .Where(cu => cu.Email == oldUser.Email && cu.IsActive == true)
                            .ToList();

                        companyUsers.AddRange(companyUsersByEmail);
                    }

                    // Benzersiz CompanyUser'ları al
                    companyUsers = companyUsers.GroupBy(cu => cu.CompanyUserID)
                                             .Select(g => g.First())
                                             .ToList();

                    // CompanyUser kayıtlarını güncelle
                    foreach (var companyUser in companyUsers)
                    {
                        bool updated = false;

                        // Email güncelle
                        if (companyUser.Email != updatedUser.Email)
                        {
                            companyUser.Email = updatedUser.Email;
                            updated = true;
                        }

                        // Ad soyad güncelle
                        string newFullName = $"{updatedUser.FirstName} {updatedUser.LastName}".Trim();
                        if (companyUser.Name != newFullName)
                        {
                            companyUser.Name = newFullName;
                            updated = true;
                        }

                        if (updated)
                        {
                            companyUser.UpdatedDate = DateTime.Now;
                            context.CompanyUsers.Update(companyUser);
                        }
                    }

                    context.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw to avoid breaking the main User update
                // Bu hata ana User güncelleme işlemini bozmasın diye sadece log'la
                System.Diagnostics.Debug.WriteLine($"CompanyUser sync error: {ex.Message}");
            }
        }

    }
}
